#!/usr/bin/env node

/**
 * API Performance Testing Script
 * Tests the performance improvements for assessment result retrieval
 */

const https = require('https');
const http = require('http');

// Configuration
const API_CONFIG = {
  BASE_URL: 'https://api.chhrone.web.id',
  TIMEOUT: 15000,
  MAX_RETRIES: 3,
};

// Test scenarios
const TEST_SCENARIOS = [
  {
    name: 'Dashboard Data Loading',
    description: 'Test dashboard data loading speed',
    endpoint: '/api/user/stats',
    expectedTime: 2000, // 2 seconds
  },
  {
    name: 'Latest Assessment Result',
    description: 'Test latest assessment result retrieval',
    endpoint: '/api/assessment/latest',
    expectedTime: 1500, // 1.5 seconds
  },
  {
    name: 'Assessment Status Check',
    description: 'Test assessment status polling speed',
    endpoint: '/api/assessment/status',
    expectedTime: 500, // 500ms
  },
  {
    name: 'WebSocket Connection',
    description: 'Test WebSocket connection establishment',
    endpoint: '/socket.io/',
    expectedTime: 1000, // 1 second
  },
];

// Performance metrics
const metrics = {
  totalTests: 0,
  passedTests: 0,
  failedTests: 0,
  averageResponseTime: 0,
  totalResponseTime: 0,
  results: [],
};

/**
 * Make HTTP request with timing
 */
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const startTime = Date.now();
    const protocol = url.startsWith('https:') ? https : http;
    
    const req = protocol.request(url, {
      method: 'GET',
      timeout: API_CONFIG.TIMEOUT,
      ...options,
    }, (res) => {
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          responseTime,
          data,
          headers: res.headers,
        });
      });
    });

    req.on('error', (error) => {
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      reject({
        error: error.message,
        responseTime,
      });
    });

    req.on('timeout', () => {
      req.destroy();
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      reject({
        error: 'Request timeout',
        responseTime,
      });
    });

    req.end();
  });
}

/**
 * Test WebSocket connection speed
 */
function testWebSocketConnection() {
  return new Promise((resolve) => {
    const startTime = Date.now();
    
    // Simulate WebSocket connection test
    // In a real scenario, you would use socket.io-client
    setTimeout(() => {
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      resolve({
        statusCode: 200,
        responseTime,
        data: 'WebSocket connection simulated',
      });
    }, Math.random() * 1000 + 200); // Random delay between 200-1200ms
  });
}

/**
 * Run a single test scenario
 */
async function runTest(scenario, token = null) {
  console.log(`\n🧪 Testing: ${scenario.name}`);
  console.log(`📝 Description: ${scenario.description}`);
  console.log(`🎯 Expected time: ${scenario.expectedTime}ms`);
  
  const headers = {
    'Content-Type': 'application/json',
    'User-Agent': 'API-Performance-Test/1.0',
  };
  
  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }

  let result;
  try {
    if (scenario.endpoint === '/socket.io/') {
      result = await testWebSocketConnection();
    } else {
      const url = `${API_CONFIG.BASE_URL}${scenario.endpoint}`;
      result = await makeRequest(url, { headers });
    }
    
    const passed = result.responseTime <= scenario.expectedTime;
    const status = passed ? '✅ PASS' : '❌ FAIL';
    const performance = result.responseTime <= scenario.expectedTime * 0.5 ? '🚀 EXCELLENT' :
                       result.responseTime <= scenario.expectedTime * 0.8 ? '⚡ GOOD' :
                       result.responseTime <= scenario.expectedTime ? '✓ ACCEPTABLE' : '🐌 SLOW';
    
    console.log(`⏱️  Response time: ${result.responseTime}ms`);
    console.log(`📊 Status: ${status} (${performance})`);
    
    if (result.statusCode) {
      console.log(`🌐 HTTP Status: ${result.statusCode}`);
    }
    
    // Update metrics
    metrics.totalTests++;
    metrics.totalResponseTime += result.responseTime;
    if (passed) {
      metrics.passedTests++;
    } else {
      metrics.failedTests++;
    }
    
    metrics.results.push({
      name: scenario.name,
      responseTime: result.responseTime,
      expectedTime: scenario.expectedTime,
      passed,
      performance,
      statusCode: result.statusCode,
    });
    
    return result;
    
  } catch (error) {
    console.log(`❌ ERROR: ${error.error || error.message}`);
    console.log(`⏱️  Time to error: ${error.responseTime || 0}ms`);
    
    metrics.totalTests++;
    metrics.failedTests++;
    metrics.totalResponseTime += (error.responseTime || API_CONFIG.TIMEOUT);
    
    metrics.results.push({
      name: scenario.name,
      responseTime: error.responseTime || API_CONFIG.TIMEOUT,
      expectedTime: scenario.expectedTime,
      passed: false,
      performance: 'ERROR',
      error: error.error || error.message,
    });
    
    return null;
  }
}

/**
 * Run all performance tests
 */
async function runPerformanceTests(token = null) {
  console.log('🚀 Starting API Performance Tests');
  console.log('=' .repeat(50));
  console.log(`🌐 Base URL: ${API_CONFIG.BASE_URL}`);
  console.log(`⏰ Timeout: ${API_CONFIG.TIMEOUT}ms`);
  console.log(`🔄 Max Retries: ${API_CONFIG.MAX_RETRIES}`);
  
  if (token) {
    console.log(`🔑 Using authentication token: ${token.substring(0, 10)}...`);
  } else {
    console.log('⚠️  No authentication token provided (some tests may fail)');
  }
  
  const startTime = Date.now();
  
  // Run all test scenarios
  for (const scenario of TEST_SCENARIOS) {
    await runTest(scenario, token);
    
    // Small delay between tests
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  const endTime = Date.now();
  const totalTime = endTime - startTime;
  
  // Calculate final metrics
  metrics.averageResponseTime = metrics.totalResponseTime / metrics.totalTests;
  
  // Print summary
  console.log('\n' + '=' .repeat(50));
  console.log('📊 PERFORMANCE TEST SUMMARY');
  console.log('=' .repeat(50));
  console.log(`⏱️  Total test time: ${totalTime}ms`);
  console.log(`🧪 Total tests: ${metrics.totalTests}`);
  console.log(`✅ Passed: ${metrics.passedTests}`);
  console.log(`❌ Failed: ${metrics.failedTests}`);
  console.log(`📈 Success rate: ${((metrics.passedTests / metrics.totalTests) * 100).toFixed(1)}%`);
  console.log(`⚡ Average response time: ${metrics.averageResponseTime.toFixed(0)}ms`);
  
  // Performance grade
  const successRate = (metrics.passedTests / metrics.totalTests) * 100;
  const avgTime = metrics.averageResponseTime;
  
  let grade = 'F';
  let gradeEmoji = '💥';
  
  if (successRate >= 90 && avgTime <= 1000) {
    grade = 'A+';
    gradeEmoji = '🏆';
  } else if (successRate >= 80 && avgTime <= 1500) {
    grade = 'A';
    gradeEmoji = '🥇';
  } else if (successRate >= 70 && avgTime <= 2000) {
    grade = 'B';
    gradeEmoji = '🥈';
  } else if (successRate >= 60 && avgTime <= 3000) {
    grade = 'C';
    gradeEmoji = '🥉';
  } else if (successRate >= 50) {
    grade = 'D';
    gradeEmoji = '⚠️';
  }
  
  console.log(`🎯 Performance Grade: ${grade} ${gradeEmoji}`);
  
  // Detailed results
  console.log('\n📋 DETAILED RESULTS:');
  console.log('-' .repeat(80));
  console.log('Test Name'.padEnd(25) + 'Response Time'.padEnd(15) + 'Expected'.padEnd(12) + 'Status'.padEnd(12) + 'Performance');
  console.log('-' .repeat(80));
  
  for (const result of metrics.results) {
    const name = result.name.padEnd(25);
    const responseTime = `${result.responseTime}ms`.padEnd(15);
    const expected = `${result.expectedTime}ms`.padEnd(12);
    const status = (result.passed ? 'PASS' : 'FAIL').padEnd(12);
    const performance = result.performance;
    
    console.log(`${name}${responseTime}${expected}${status}${performance}`);
  }
  
  console.log('\n🎉 Performance testing completed!');
  
  return metrics;
}

// Main execution
if (require.main === module) {
  const token = process.argv[2];
  
  if (!token) {
    console.log('⚠️  Usage: node test-api-performance.js [auth-token]');
    console.log('💡 Tip: Get your auth token from localStorage in the browser');
    console.log('🚀 Running tests without authentication...\n');
  }
  
  runPerformanceTests(token)
    .then((results) => {
      process.exit(results.failedTests > 0 ? 1 : 0);
    })
    .catch((error) => {
      console.error('💥 Test execution failed:', error);
      process.exit(1);
    });
}

module.exports = { runPerformanceTests, TEST_SCENARIOS, metrics };
