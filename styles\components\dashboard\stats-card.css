/* Stats Card Component Styles */
.stats-card {
  @apply rounded-2xl border text-card-foreground shadow-sm bg-white;
  border-color: #eaecf0;
}

.stats-card__content {
  @apply p-4;
}

.stats-card__container {
  @apply flex items-center justify-between;
}

.stats-card__text-container {
  /* Container for value and label */
}

.stats-card__value {
  @apply text-3xl font-bold;
  color: #1e1e1e;
}

.stats-card__label {
  @apply text-xs;
  color: #64707d;
}

.stats-card__icon-container {
  @apply w-10 h-10 rounded-full flex items-center justify-center;
  /* Background color will be set dynamically via style prop */
}

.stats-card__icon {
  @apply w-6 h-6;
}

/* Responsive adjustments for stats cards */
/* Mobile: Optimize for small screens */
@media (max-width: 640px) {
  .stats-card {
    @apply min-h-[100px];
  }

  .stats-card__content {
    @apply p-3;
  }

  .stats-card__container {
    @apply flex-col items-center text-center gap-2;
  }

  .stats-card__value {
    @apply text-2xl font-bold;
  }

  .stats-card__label {
    @apply text-sm;
    line-height: 1.2;
  }

  .stats-card__icon-container {
    @apply w-8 h-8;
  }

  .stats-card__icon {
    @apply w-5 h-5;
  }
}

/* Tablet: Medium optimization */
@media (min-width: 641px) and (max-width: 1023px) {
  .stats-card {
    @apply min-h-[110px];
  }

  .stats-card__content {
    @apply p-4;
  }

  .stats-card__value {
    @apply text-3xl;
  }

  .stats-card__label {
    @apply text-sm;
  }

  .stats-card__icon-container {
    @apply w-9 h-9;
  }

  .stats-card__icon {
    @apply w-5 h-5;
  }
}

/* Desktop: Preserve original sizes */
@media (min-width: 1024px) {
  .stats-card__content {
    @apply p-4;
  }

  .stats-card__container {
    @apply flex items-center justify-between;
  }

  .stats-card__value {
    @apply text-3xl font-bold;
  }

  .stats-card__label {
    @apply text-xs;
  }

  .stats-card__icon-container {
    @apply w-10 h-10;
  }

  .stats-card__icon {
    @apply w-6 h-6;
  }
}
